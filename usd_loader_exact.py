"""
精确USD加载器 - 完全模拟双击打开USD文件的行为
"""

from isaacsim import SimulationApp

# 启动 Isaac Sim
simulation_app = SimulationApp({"headless": False})

import carb
import omni
import os
from isaacsim.core.api import SimulationContext

def main():
    """主函数：完全模拟双击打开USD文件"""
    print("🚀 启动 Isaac Sim - 精确USD加载模式")
    print("=" * 60)
    
    try:
        usd_file_path = "ROV_THRUSTERS.usd"
        
        if not os.path.exists(usd_file_path):
            print(f"❌ 未找到文件: {usd_file_path}")
            print("请确保 ROV_THRUSTERS.usd 文件在脚本同目录下")
            return False
        
        print(f"📁 找到文件: {usd_file_path}")
        
        # 获取绝对路径
        abs_path = os.path.abspath(usd_file_path)
        print(f"📍 绝对路径: {abs_path}")
        
        # 方法1: 使用 omni.usd 直接打开（最接近双击行为）
        print("🔄 方法1: 直接打开USD文件作为主场景...")
        try:
            # 关闭当前场景
            omni.usd.get_context().close_stage()
            
            # 直接打开USD文件
            success = omni.usd.get_context().open_stage(abs_path)
            
            if success:
                print("✅ 方法1成功: USD文件已作为主场景打开")
                stage = omni.usd.get_context().get_stage()
                
                # 打印场景结构信息
                print("📋 场景结构:")
                root_prim = stage.GetPseudoRoot()
                for child in root_prim.GetChildren():
                    print(f"  📁 /{child.GetName()}")
                    # 显示子级别
                    for subchild in child.GetChildren():
                        print(f"    📄 /{child.GetName()}/{subchild.GetName()}")
                
            else:
                raise Exception("open_stage 返回 False")
                
        except Exception as e:
            print(f"⚠️ 方法1失败: {e}")
            
            # 方法2: 备用 - 使用 Sublayer
            print("🔄 方法2: 使用 Sublayer 方式...")
            try:
                stage = omni.usd.get_context().get_stage()
                stage.GetRootLayer().subLayerPaths.append(abs_path)
                print("✅ 方法2成功: USD文件已作为子层加载")
            except Exception as e2:
                print(f"⚠️ 方法2也失败: {e2}")
                
                # 方法3: 最后备用 - 引用方式
                print("🔄 方法3: 使用引用方式...")
                stage = omni.usd.get_context().get_stage()
                root_prim = stage.DefinePrim("/World", "Xform")
                root_prim.GetReferences().AddReference(abs_path)
                print("✅ 方法3成功: USD文件已作为引用加载")
        
        # 等待场景完全加载
        print("⏳ 等待场景加载完成...")
        for i in range(10):
            simulation_app.update()
            import time
            time.sleep(0.1)
        
        # 启动物理仿真（如果需要）
        try:
            physx_interface = omni.physx.acquire_physx_interface()
            physx_interface.start_simulation()
            print("✅ 物理仿真已启动")
        except Exception as e:
            print(f"⚠️ 物理仿真启动警告: {e}")
        
        print("=" * 60)
        print("✅ USD文件加载完成！")
        print("💡 现在的场景应该和您双击打开的完全一样")
        print("💡 按 Ctrl+C 可以停止程序")
        print("=" * 60)
        
        # 静默运行
        frame_count = 0
        while True:
            simulation_app.update()
            
            # 每30秒输出一次状态
            if frame_count % 1800 == 0 and frame_count > 0:
                print(f"🔄 运行中... ({frame_count // 60} 秒)")
            
            frame_count += 1
            
            # 60 FPS
            import time
            time.sleep(1.0 / 60.0)
            
    except KeyboardInterrupt:
        print("\n🛑 用户停止程序")
        return True
    except Exception as e:
        print(f"\n❌ 运行错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("🔄 关闭 Isaac Sim...")
        simulation_app.close()
        print("✅ 程序结束")
