"""
快速环境加载测试脚本
专门用于测试 USD 环境文件加载，最小化的代码
"""

from isaacsim import SimulationApp

# 启动 Isaac Sim
print("🚀 启动 Isaac Sim...")
simulation_app = SimulationApp({"headless": False})

try:
    import carb
    import omni
    import os
    from isaacsim.core.api import SimulationContext
    from pxr import UsdGeom, Gf, UsdPhysics, Usd

    print("✅ 所有模块导入成功")

    # 初始化核心组件
    print("🔧 初始化核心组件...")
    stage = simulation_app.context.get_stage()
    sim_context = SimulationContext(stage_units_in_meters=1.0)
    physx_interface = omni.physx.acquire_physx_interface()

    print("✅ 核心组件初始化完成")

    # 加载 USD 环境文件
    print("🌊 尝试加载 USD 环境文件...")
    usd_file_path = "ROV_THRUSTERS.usd"

    if os.path.exists(usd_file_path):
        print(f"📁 找到 USD 文件: {usd_file_path}")
        try:
            # 创建环境 prim 并加载 USD
            env_prim = stage.DefinePrim("/World/Environment", "Xform")
            env_prim.GetReferences().AddReference(usd_file_path)
            print(f"✅ 成功加载环境: {usd_file_path}")
        except Exception as e:
            print(f"❌ 加载环境失败: {e}")
            raise
    else:
        print(f"⚠️ 未找到 USD 文件: {usd_file_path}")
        print("🔧 创建基础水下环境...")
        
        # 创建简单的水面
        water_surface = UsdGeom.Mesh.Define(stage, "/World/WaterSurface")
        water_surface.CreatePointsAttr([
            (-50, -50, 0), (50, -50, 0), (50, 50, 0), (-50, 50, 0)
        ])
        water_surface.CreateFaceVertexIndicesAttr([0, 1, 2, 3])
        water_surface.CreateFaceVertexCountsAttr([4])
        water_surface.CreateDisplayColorAttr([(0.2, 0.6, 0.8)])
        
        print("✅ 基础环境创建完成")

    # 启动仿真
    print("🔄 启动仿真...")
    try:
        physx_interface.start_simulation()
        sim_context._timeline.play()
        print("✅ 仿真启动成功")
    except Exception as e:
        print(f"⚠️ 仿真启动警告: {e}")

    print("\n" + "="*60)
    print("✅ 环境加载测试完成！")
    print("📋 请检查:")
    print("  • Isaac Sim 窗口是否打开？")
    print("  • 是否看到了环境场景？")
    print("  • 控制台是否没有错误信息？")
    print("="*60)

    print("\n🔄 环境将持续运行...")
    print("💡 按 Ctrl+C 停止程序")

    # 持续运行
    frame_count = 0
    while True:
        simulation_app.update()
        
        # 每 5 秒输出一次状态
        if frame_count % 300 == 0:
            print(f"🔄 运行中... 帧数: {frame_count}")
        
        frame_count += 1
        
        # 控制帧率
        import time
        time.sleep(1.0 / 60.0)

except KeyboardInterrupt:
    print("\n🛑 用户中断")
except Exception as e:
    print(f"\n❌ 错误: {e}")
    import traceback
    traceback.print_exc()
finally:
    print("\n🔄 关闭 Isaac Sim...")
    try:
        sim_context._timeline.stop()
    except:
        pass
    simulation_app.close()
    print("✅ 程序结束")
