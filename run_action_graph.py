"""
Simple Multi-ROV System - All 3 ROVs
Simplified version that avoids API checking issues
"""

import omni.graph.core as og
import omni.kit.commands
import carb
import omni.usd
from pxr import UsdGeom, Gf, UsdPhysics


class SimpleMultiROVSystem:
    """Simple system for all 3 ROVs without complex API checks"""
    
    def __init__(self):
        self.graph_path = "/SimpleMultiROVSystem"
        
        # All 3 ROVs found in your scene
        self.rov_configs = [
            {
                "name": "ROV_Original",
                "path": "/World/ROV",
                # "mass": 800,
                # "size": 1.8,
                # "target_depth": -1.5,
                # "color": (0.8, 0.2, 0.2),  # Red
            },
            {
                "name": "ROV_Main", 
                "path": "/World/ROV_Main",
                "mass": 1000,
                "size": 2.0,
                "target_depth": -4.0,
                "color": (0.2, 0.4, 0.8),  # Blue
            },
            {
                "name": "ROV_Scout",
                "path": "/World/ROV_Scout", 
                "mass": 500,
                "size": 1.6,
                "target_depth": -2.0,
                "color": (0.2, 0.8, 0.4),  # Green
            }
        ]
    
    def ensure_rov_physics(self):
        """Ensure all ROVs have basic physics - simplified approach"""
        print("🤖 Ensuring physics for all 3 ROVs...")
        
        try:
            stage = omni.usd.get_context().get_stage()
            
            for rov in self.rov_configs:
                print(f"\n🔧 Checking {rov['name']} at {rov['path']}...")
                
                prim = stage.GetPrimAtPath(rov["path"])
                
                if prim.IsValid():
                    print(f"  ✅ Found existing ROV: {rov['path']}")
                    
                    # Simply apply physics APIs without checking (they won't duplicate)
                    try:
                        UsdPhysics.RigidBodyAPI.Apply(prim)
                        UsdPhysics.CollisionAPI.Apply(prim)
                        mass_api = UsdPhysics.MassAPI.Apply(prim)
                        mass_api.CreateMassAttr(rov["mass"])
                        print(f"  ✅ Physics APIs applied")
                    except Exception as e:
                        print(f"  ⚠️ Physics APIs may already exist: {e}")
                        
                else:
                    print(f"  ❌ ROV not found: {rov['path']}")
                    print(f"  Creating new ROV...")
                    
                    # Create new ROV
                    cube_geom = UsdGeom.Cube.Define(stage, rov["path"])
                    cube_geom.CreateSizeAttr(rov["size"])
                    cube_geom.AddTranslateOp().Set(Gf.Vec3f(0, 0, rov["target_depth"]))
                    cube_geom.CreateDisplayColorAttr([rov["color"]])
                    
                    # Add physics
                    prim = cube_geom.GetPrim()
                    UsdPhysics.RigidBodyAPI.Apply(prim)
                    UsdPhysics.CollisionAPI.Apply(prim)
                    mass_api = UsdPhysics.MassAPI.Apply(prim)
                    mass_api.CreateMassAttr(rov["mass"])
                    
                    print(f"  ✅ Created new ROV with physics")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to setup ROVs: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def create_multi_rov_physics_graph(self):
        """Create physics graph for all 3 ROVs"""
        print("\n🌊 Creating multi-ROV physics system...")
        
        # Delete existing
        try:
            omni.kit.commands.execute('DeletePrims', paths=[self.graph_path])
        except:
            pass
        
        try:
            keys = og.Controller.Keys
            (graph_handle, nodes_created, _, _) = og.Controller.edit(
                {"graph_path": self.graph_path, "evaluator_name": "execution"},
                {
                    keys.CREATE_NODES: [
                        # Basic execution
                        ("tick", "omni.graph.action.OnTick"),
                        
                        # Physics scripts for each ROV
                        ("rov_original_physics", "omni.graph.scriptnode.ScriptNode"),
                        ("rov_main_physics", "omni.graph.scriptnode.ScriptNode"),
                        ("rov_scout_physics", "omni.graph.scriptnode.ScriptNode"),
                        
                        # Environmental system
                        ("environmental_system", "omni.graph.scriptnode.ScriptNode"),
                        
                        # Multi-ROV monitor
                        ("multi_rov_monitor", "omni.graph.scriptnode.ScriptNode"),
                        
                        # Output
                        ("debug_print", "omni.graph.ui_nodes.PrintText"),
                    ],
                    keys.SET_VALUES: [
                        ("debug_print.inputs:text", "Simple Multi-ROV System Active"),
                        ("debug_print.inputs:logLevel", "Info"),
                        
                        # Set scripts for each ROV
                        ("rov_original_physics.inputs:script", self.get_rov_physics_script(0)),
                        ("rov_main_physics.inputs:script", self.get_rov_physics_script(1)),
                        ("rov_scout_physics.inputs:script", self.get_rov_physics_script(2)),
                        ("environmental_system.inputs:script", self.get_environmental_script()),
                        ("multi_rov_monitor.inputs:script", self.get_multi_monitor_script()),
                    ],
                    keys.CONNECT: [
                        # Execution chain
                        ("tick.outputs:tick", "environmental_system.inputs:execIn"),
                        ("environmental_system.outputs:execOut", "rov_original_physics.inputs:execIn"),
                        ("rov_original_physics.outputs:execOut", "rov_main_physics.inputs:execIn"),
                        ("rov_main_physics.outputs:execOut", "rov_scout_physics.inputs:execIn"),
                        ("rov_scout_physics.outputs:execOut", "multi_rov_monitor.inputs:execIn"),
                        ("multi_rov_monitor.outputs:execOut", "debug_print.inputs:execIn"),
                    ],
                },
            )
            
            print(f"✅ Multi-ROV physics graph created: {graph_handle}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to create physics graph: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_rov_physics_script(self, rov_index):
        """Get physics script for specific ROV"""
        rov = self.rov_configs[rov_index]
        
        return f'''
import math
import omni.usd
import time

def setup(db):
    db.rov_name = "{rov["name"]}"
    db.rov_path = "{rov["path"]}"
    db.mass = {rov["mass"]}
    db.size = {rov["size"]}
    db.volume = {rov["size"]} ** 3  # Cube volume
    db.height = {rov["size"]}
    db.target_depth = {rov["target_depth"]}
    db.water_density = 1025.0
    db.gravity = 9.81
    db.last_position = [0, 0, {rov["target_depth"]}]
    db.last_time = time.time()
    db.frame_count = 0
    db.control_gain = {300 if rov["mass"] < 600 else 500 if rov["mass"] < 900 else 700}
    print(f"{{db.rov_name}} physics initialized - Mass: {{db.mass}}kg, Target: {{db.target_depth}}m")

def compute(db):
    db.frame_count += 1
    current_time = time.time()
    
    try:
        stage = omni.usd.get_context().get_stage()
        rov_prim = stage.GetPrimAtPath(db.rov_path)
        
        if rov_prim.IsValid():
            xform = rov_prim.GetAttribute("xformOp:translate")
            if xform:
                position = xform.Get()
                if position:
                    pos = [position[0], position[1], position[2]]
                    
                    # Calculate depth (water level at Z=0)
                    depth = 0.0 - pos[2]
                    
                    if depth > 0:
                        # Submerged physics
                        submerged_ratio = min(1.0, depth / db.height)
                        submerged_volume = db.volume * submerged_ratio
                        
                        # Buoyancy force
                        buoyancy_force = db.water_density * submerged_volume * db.gravity
                        
                        # Depth control
                        depth_error = db.target_depth - pos[2]
                        control_force = depth_error * db.control_gain
                        
                        # Drag simulation (simple)
                        velocity_z = (pos[2] - db.last_position[2]) / max(current_time - db.last_time, 0.001)
                        drag_force = -velocity_z * abs(velocity_z) * 50.0  # Quadratic drag
                        
                        total_force = buoyancy_force + control_force + drag_force
                        
                        # Log every 3 seconds
                        if db.frame_count % 180 == 0:
                            print(f"{{db.rov_name}}: depth={{depth:.2f}}m, buoyancy={{buoyancy_force:.0f}}N, control={{control_force:.0f}}N, drag={{drag_force:.0f}}N")
                        
                        # Store results
                        db.outputs.buoyancy_force = buoyancy_force
                        db.outputs.control_force = control_force
                        db.outputs.drag_force = drag_force
                        db.outputs.total_force = total_force
                        db.outputs.depth = depth
                        db.outputs.depth_error = depth_error
                        
                    else:
                        # Above water
                        if db.frame_count % 180 == 0:
                            print(f"{{db.rov_name}} above water at Z={{pos[2]:.2f}}m")
                        
                        db.outputs.buoyancy_force = 0
                        db.outputs.control_force = 0
                        db.outputs.drag_force = 0
                        db.outputs.total_force = -db.mass * db.gravity
                        db.outputs.depth = 0
                        db.outputs.depth_error = db.target_depth - pos[2]
                    
                    db.last_position = pos
                    db.last_time = current_time
    
    except Exception as e:
        if db.frame_count % 180 == 0:
            print(f"{{db.rov_name}} physics error: {{e}}")
'''
    
    def get_environmental_script(self):
        """Environmental effects for all ROVs"""
        return '''
import math
import time

def setup(db):
    db.start_time = time.time()
    db.wave_amplitude = 0.4
    db.current_strength = 0.3
    db.turbulence_factor = 0.15
    db.frame_count = 0
    print("Environmental system initialized for 3 ROVs")

def compute(db):
    db.frame_count += 1
    sim_time = time.time() - db.start_time
    
    # Multi-frequency wave system
    wave_height = db.wave_amplitude * math.sin(sim_time * 0.4)
    wave_height += 0.2 * math.sin(sim_time * 1.1)
    wave_height += 0.1 * math.sin(sim_time * 2.3)
    
    # Dynamic current with turbulence
    base_current_dir = math.sin(sim_time * 0.08) * math.pi
    turbulence = math.sin(sim_time * 3.0) * db.turbulence_factor
    
    current_x = db.current_strength * math.cos(base_current_dir + turbulence)
    current_y = db.current_strength * math.sin(base_current_dir + turbulence)
    current_z = math.sin(sim_time * 0.5) * 0.1  # Vertical current
    
    # Temperature gradient (affects density)
    temp_surface = 25.0  # Celsius
    temp_gradient = -0.1  # Per meter depth
    
    # Log every 6 seconds
    if db.frame_count % 360 == 0:
        print(f"Environment: wave={{wave_height:.2f}}m, current=[{current_x:.2f}, {current_y:.2f}, {current_z:.2f}]m/s, temp_surface={{temp_surface:.1f}}°C")
    
    db.outputs.wave_height = wave_height
    db.outputs.current_vector = [current_x, current_y, current_z]
    db.outputs.current_strength = math.sqrt(current_x**2 + current_y**2)
    db.outputs.temperature_surface = temp_surface
    db.outputs.temperature_gradient = temp_gradient
    db.outputs.sim_time = sim_time
'''
    
    def get_multi_monitor_script(self):
        """Monitor all 3 ROVs"""
        return '''
import time

def setup(db):
    db.start_time = time.time()
    db.last_report_time = time.time()
    db.report_interval = 4.0  # Report every 4 seconds
    db.frame_count = 0
    db.rov_names = ["ROV_Original", "ROV_Main", "ROV_Scout"]
    print("Multi-ROV monitoring system initialized")

def compute(db):
    db.frame_count += 1
    current_time = time.time()
    
    if current_time - db.last_report_time >= db.report_interval:
        sim_time = current_time - db.start_time
        fps = db.frame_count / sim_time if sim_time > 0 else 60
        
        print(f"\\n=== Multi-ROV System Status (t={{sim_time:.1f}}s, fps={{fps:.1f}}) ===")
        print("✅ Simple 3-ROV underwater physics system running")
        print("🔴 ROV_Original: Lightweight, shallow depth (-1.5m)")
        print("🔵 ROV_Main: Heavy-duty, deep exploration (-4.0m)")  
        print("🟢 ROV_Scout: Medium, reconnaissance (-2.0m)")
        print("🌊 Environmental effects: waves, currents, temperature")
        print("🎯 Individual depth control for each ROV")
        print("📊 Real-time physics: buoyancy, drag, control forces")
        
        db.last_report_time = current_time
    
    db.outputs.system_status = "ALL_3_ROVS_ACTIVE"
    db.outputs.uptime = current_time - db.start_time
    db.outputs.total_rovs = 3
'''


def main():
    """Main function to create simple multi-ROV system"""
    system = SimpleMultiROVSystem()
    
    print("=" * 70)
    print("🤖 SIMPLE MULTI-ROV UNDERWATER SYSTEM")
    print("=" * 70)
    
    # Step 1: Ensure ROV physics
    print("\n1. Ensuring physics for all 3 ROVs...")
    if not system.ensure_rov_physics():
        return False
    
    # Step 2: Create physics graph
    print("\n2. Creating multi-ROV physics system...")
    if not system.create_multi_rov_physics_graph():
        return False
    
    print("\n" + "=" * 70)
    print("🚀 SIMPLE 3-ROV SYSTEM CREATED!")
    print("=" * 70)
    
    print("\n🤖 ROV Fleet (All 3 ROVs):")
    for i, rov in enumerate(system.rov_configs, 1):
        color_emoji = "🔴" if "Original" in rov["name"] else "🔵" if "Main" in rov["name"] else "🟢"
        print(f"{color_emoji} {rov['name']}: {rov['size']}m cube, {rov['mass']}kg, target {rov['target_depth']}m")
    
    print("\n🌊 Features:")
    print("✅ Individual physics simulation for each ROV")
    print("✅ Realistic buoyancy, drag, and control forces")
    print("✅ Environmental effects: waves, currents, temperature")
    print("✅ Adaptive control gains based on ROV mass")
    print("✅ Multi-ROV monitoring and logging")
    print("✅ Simplified, stable implementation")
    
    print("\n📊 Action Graph: /SimpleMultiROVSystem")
    
    print("\n🎯 Expected Behavior:")
    print("• ROV_Original (Red): Light, quick response, shallow depth")
    print("• ROV_Main (Blue): Heavy, stable, deep exploration")
    print("• ROV_Scout (Green): Balanced, medium depth reconnaissance")
    
    print("\n🚀 Next Steps:")
    print("1. Click PLAY to start 3-ROV simulation")
    print("2. Watch console for individual ROV physics")
    print("3. Observe different behaviors based on mass/size")
    print("4. See environmental effects affecting all ROVs")
    print("5. Monitor system status every 4 seconds")
    
    return True


if __name__ == "__main__":
    main()
