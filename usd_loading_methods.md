# USD 文件加载方式对比

## 🎯 问题分析

您发现直接双击打开 `ROV_THRUSTERS.usd` 和脚本加载的结果不同，这是因为加载方式不同导致的场景结构差异。

## 🔄 不同的加载方式

### 方式1: 双击打开（您的参考标准）
```
当您双击 ROV_THRUSTERS.usd 文件时：
- Isaac Sim 将整个 USD 文件作为主场景打开
- 文件中的所有内容直接出现在场景根级别
- 场景结构完全按照 USD 文件内部结构显示
```

### 方式2: 引用加载（之前脚本的方式）
```python
# 这种方式会改变场景结构
env_prim = stage.DefinePrim("/World/Environment", "Xform")
env_prim.GetReferences().AddReference(usd_file_path)

结果：
/World
  /Environment  <- 新创建的容器
    /[USD文件内容]  <- USD内容被包装在这里
```

### 方式3: 直接打开（新脚本的方式）
```python
# 这种方式模拟双击行为
omni.usd.get_context().open_stage(abs_path)

结果：
/[USD文件的根结构]  <- 直接显示USD文件内容
/World  <- 如果USD文件中有World
/Environment  <- 如果USD文件中有Environment
```

## 🚀 推荐使用的脚本

### 1. `usd_loader_exact.py` - 精确模拟双击
- ✅ 完全模拟双击打开行为
- ✅ 保持原始场景结构
- ✅ 多种备用加载方式
- ✅ 显示场景结构信息

### 2. `simple_usd_loader.py` - 改进版
- ✅ 优先使用直接打开方式
- ✅ 备用引用方式
- ✅ 更简洁的代码

## 🔍 验证方法

运行脚本后，检查以下内容：

### 场景结构对比
**双击打开时您看到的结构**:
```
Scene
├── /World
│   └── [World中的内容]
└── /Environment
    └── [Environment中的内容]
```

**脚本加载后应该看到相同结构**:
```
Scene
├── /World
│   └── [相同的World内容]
└── /Environment
    └── [相同的Environment内容]
```

### 检查清单
- [ ] Isaac Sim 窗口显示相同的视觉内容
- [ ] Scene Hierarchy 面板显示相同的结构
- [ ] World 和 Environment 都在根级别
- [ ] 各个对象的位置和属性相同

## 🛠️ 故障排除

### 如果场景结构仍然不同

1. **检查USD文件内容**:
   ```python
   # 可以添加这段代码查看USD文件结构
   from pxr import Usd
   stage = Usd.Stage.Open("ROV_THRUSTERS.usd")
   for prim in stage.Traverse():
       print(f"USD文件中的Prim: {prim.GetPath()}")
   ```

2. **尝试不同的加载方式**:
   - `usd_loader_exact.py` 会自动尝试3种不同方式
   - 查看控制台输出，看哪种方式成功了

3. **检查文件路径**:
   - 确保使用绝对路径
   - 确保文件没有损坏

### 如果物理行为不同

可能是因为：
- 物理仿真启动时机不同
- 场景加载完成度不同
- 需要等待更长时间让场景完全加载

## 📋 使用建议

1. **首先尝试** `usd_loader_exact.py`:
   ```bash
   python usd_loader_exact.py
   ```

2. **观察控制台输出**，确认使用了哪种加载方式

3. **检查场景结构**，确认和双击打开的结果一致

4. **如果仍有差异**，请告诉我：
   - 控制台的具体输出
   - 场景结构的差异
   - 视觉上的不同之处

这样我们就能精确地复制您双击打开USD文件的行为！
