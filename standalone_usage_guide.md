# Isaac Sim 独立模式多 ROV 系统使用指南

## 📋 概述

基于官方 Isaac Sim standalone 模式最佳实践，重新编写的多 ROV 水下仿真系统。

## 🔧 关键改进

### 1. 遵循官方 Standalone 架构
```python
# 官方推荐的结构
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": False})

# 然后导入其他模块
import carb
import omni
from isaacsim.core.api import SimulationContext
from pxr import UsdGeom, Gf, UsdPhysics
from omni.isaac.dynamic_control import _dynamic_control

# 使用官方 API
stage = simulation_app.context.get_stage()
sim_context = SimulationContext(stage_units_in_meters=1.0)
physx_interface = omni.physx.acquire_physx_interface()
```

### 2. 简化的执行流程
```python
# 启动物理
physx_interface.start_simulation()
physx_interface.force_load_physics_from_usd()

# 创建 ROV
for rov_config in rov_configs:
    prim = create_rov_prim(stage, rov_config)

# 运行仿真循环
sim_context._timeline.play()
while simulation_time < max_time:
    # 更新物理
    physx_interface.update_simulation(elapsedStep=dt, currentTime=simulation_time)
    # 更新 ROV 逻辑
    # 更新应用
    simulation_app.update()

# 清理
sim_context._timeline.stop()
simulation_app.close()
```

## 🚀 使用方法

### 方法 1: 命令行运行（推荐）
```bash
cd /path/to/your/project
python standalone_multi_rov_system.py
```

### 方法 2: Isaac Sim Script Editor
```python
exec(open('standalone_multi_rov_system.py').read())
```

## 🎯 系统特性

### ROV 配置
- **ROV_Original**: 800kg, 1.8m, 目标深度 -1.5m (红色)
- **ROV_Main**: 1000kg, 2.0m, 目标深度 -4.0m (蓝色)  
- **ROV_Scout**: 500kg, 1.6m, 目标深度 -2.0m (绿色)

### 物理模拟
- ✅ **浮力计算**: 基于阿基米德原理
- ✅ **控制力**: 基于深度误差的 PID 控制
- ✅ **阻力**: 二次阻力模型
- ✅ **环境效果**: 波浪和洋流

### 监控功能
- 🔄 实时物理状态输出
- 📊 系统性能监控
- 🌊 环境效果显示
- 🎯 ROV 深度控制状态

## 📝 输出示例

```
======================================================================
🤖 独立模式多 ROV 水下系统
🔧 基于官方 Isaac Sim standalone 模式最佳实践
======================================================================

🤖 ROV 舰队配置:
🔴 ROV_Original: 1.8m 立方体, 800.0kg, 目标深度 -1.5m
🔵 ROV_Main: 2.0m 立方体, 1000.0kg, 目标深度 -4.0m
🟢 ROV_Scout: 1.6m 立方体, 500.0kg, 目标深度 -2.0m

🌊 功能特性:
✅ 每个 ROV 独立物理仿真
✅ 真实的浮力、阻力和控制力
✅ 环境效果: 波浪、洋流
✅ 基于质量的自适应控制增益
✅ 多 ROV 监控和日志
✅ 独立模式，无需 ActionGraph
✅ 基于官方 Isaac Sim standalone 架构

🎯 预期行为:
• ROV_Original (红色): 轻型，快速响应，浅水深度 (-1.5m)
• ROV_Main (蓝色): 重型，稳定，深海探索 (-4.0m)
• ROV_Scout (绿色): 平衡，中等深度侦察 (-2.0m)

🚀 启动独立模式多 ROV 仿真...
🔧 创建 ROV_Original 在 /World/ROV_Original
  ✅ ROV_Original 创建完成 - 质量: 800.0kg, 尺寸: 1.8m, 目标深度: -1.5m
🔧 创建 ROV_Main 在 /World/ROV_Main
  ✅ ROV_Main 创建完成 - 质量: 1000.0kg, 尺寸: 2.0m, 目标深度: -4.0m
🔧 创建 ROV_Scout 在 /World/ROV_Scout
  ✅ ROV_Scout 创建完成 - 质量: 500.0kg, 尺寸: 1.6m, 目标深度: -2.0m

🎯 仿真目标:
  🔴 ROV_Original: 目标深度 -1.5m
  🔵 ROV_Main: 目标深度 -4.0m
  🟢 ROV_Scout: 目标深度 -2.0m

🔄 开始仿真循环 (最大 30.0 秒)...
  🔴 ROV_Original: 深度=1.50m, 浮力=26325N, 控制=-0N, 阻力=0N
  🔵 ROV_Main: 深度=4.00m, 浮力=82000N, 控制=0N, 阻力=0N
  🟢 ROV_Scout: 深度=2.00m, 浮力=20480N, 控制=0N, 阻力=0N

🌊 环境: 波高=0.00m, 洋流=[0.30, 0.00]m/s

=== 多 ROV 系统状态 (t=3.0s, fps=60.0) ===
✅ 独立模式 3-ROV 水下物理系统运行中
🔴 ROV_Original: 轻型，浅水深度 (-1.5m)
🔵 ROV_Main: 重型，深海探索 (-4.0m)
🟢 ROV_Scout: 中型，侦察任务 (-2.0m)

🏁 仿真完成 - 总时间: 30.0s, 总帧数: 1800
✅ 仿真成功完成
🏁 程序执行完成: True
🔄 关闭 Isaac Sim...
```

## ⚠️ 重要注意事项

### 1. SimulationApp 生命周期
- 必须在所有其他导入之前启动 `SimulationApp`
- 必须在程序结束时调用 `simulation_app.close()`

### 2. 物理系统初始化
- 使用 `physx_interface.start_simulation()`
- 调用 `physx_interface.force_load_physics_from_usd()`

### 3. 时间轴控制
- 使用 `sim_context._timeline.play()` 启动
- 使用 `sim_context._timeline.stop()` 停止

### 4. 仿真更新
- 使用 `physx_interface.update_simulation()` 更新物理
- 使用 `simulation_app.update()` 更新应用

## 🔄 与 ActionGraph 模式对比

| 方面 | ActionGraph 模式 | 独立模式 |
|------|------------------|----------|
| **初始化** | 在已运行的 Isaac Sim 中 | 自己启动 SimulationApp |
| **执行** | OnTick 节点自动触发 | 手动仿真循环 |
| **物理** | ApplyForce 节点 | 直接 PhysX API |
| **调试** | ActionGraph 界面 | 标准 Python 调试 |
| **扩展** | 修改图结构 | 直接修改代码 |
| **性能** | 图执行开销 | 直接函数调用 |

## 🎯 下一步

1. **运行测试**: 执行 `python standalone_multi_rov_system.py`
2. **观察行为**: 查看不同 ROV 的物理响应
3. **修改参数**: 调整 ROV 配置和物理参数
4. **扩展功能**: 添加新的 ROV 或物理效果
5. **性能优化**: 根据需要调整仿真参数

这个独立模式版本完全遵循官方 Isaac Sim standalone 最佳实践，提供了更好的控制和扩展性。
