# USD 环境文件加载指南

## 📋 概述

在 ActionGraph 模式中，您会手动导入 `ROV_THRUSTERS.usd` 文件作为操作环境。在独立模式中，我们需要通过代码来加载这个环境文件。

## 🔄 ActionGraph vs 独立模式对比

### ActionGraph 模式
```
1. 启动 Isaac Sim
2. 手动导入 ROV_THRUSTERS.usd (File -> Open/Import)
3. 运行 ActionGraph 脚本
4. ROV 在已加载的环境中运行
```

### 独立模式
```python
# 1. 启动 SimulationApp
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": False})

# 2. 通过代码加载 USD 环境
def load_usd_environment():
    usd_file_path = "ROV_THRUSTERS.usd"
    env_prim = stage.DefinePrim("/World/Environment", "Xform")
    env_prim.GetReferences().AddReference(usd_file_path)

# 3. 创建 ROV 并运行仿真
```

## 🔧 USD 文件加载方法

### 方法 1: 直接引用 USD 文件
```python
def load_usd_environment():
    """加载 ROV_THRUSTERS.usd 环境"""
    usd_file_path = "ROV_THRUSTERS.usd"  # 相对路径
    # 或者使用绝对路径
    # usd_file_path = "/full/path/to/ROV_THRUSTERS.usd"
    
    try:
        # 创建环境 prim 并添加引用
        env_prim = stage.DefinePrim("/World/Environment", "Xform")
        env_prim.GetReferences().AddReference(usd_file_path)
        print(f"✅ 成功加载环境: {usd_file_path}")
        return True
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return False
```

### 方法 2: 使用 Isaac Sim 资源路径
```python
from isaacsim.storage.native import get_assets_root_path

def load_usd_environment_from_assets():
    """从 Isaac Sim 资源目录加载"""
    assets_root_path = get_assets_root_path()
    if assets_root_path is None:
        carb.log_error("Could not find Isaac Sim assets folder")
        return False
    
    # 假设您的 USD 文件在资源目录中
    usd_file_path = assets_root_path + "/Isaac/Environments/ROV_THRUSTERS.usd"
    
    env_prim = stage.DefinePrim("/World/Environment", "Xform")
    env_prim.GetReferences().AddReference(usd_file_path)
    return True
```

### 方法 3: 检查多个可能的路径
```python
def load_usd_environment_smart():
    """智能加载 USD 环境文件"""
    possible_paths = [
        "ROV_THRUSTERS.usd",                    # 当前目录
        "./ROV_THRUSTERS.usd",                  # 当前目录（显式）
        "../ROV_THRUSTERS.usd",                 # 上级目录
        "./assets/ROV_THRUSTERS.usd",           # assets 子目录
        "./environments/ROV_THRUSTERS.usd",     # environments 子目录
    ]
    
    import os
    for usd_path in possible_paths:
        if os.path.exists(usd_path):
            print(f"📁 找到 USD 文件: {usd_path}")
            try:
                env_prim = stage.DefinePrim("/World/Environment", "Xform")
                env_prim.GetReferences().AddReference(usd_path)
                print(f"✅ 成功加载环境: {usd_path}")
                return True
            except Exception as e:
                print(f"❌ 加载失败: {e}")
                continue
    
    print("⚠️ 未找到 ROV_THRUSTERS.usd，将创建基础环境")
    create_basic_underwater_environment()
    return True
```

## 🌊 备用环境创建

如果找不到 `ROV_THRUSTERS.usd` 文件，系统会自动创建一个基础的水下环境：

```python
def create_basic_underwater_environment():
    """创建基础水下环境"""
    print("🌊 创建基础水下环境...")
    
    # 水面
    water_surface = UsdGeom.Mesh.Define(stage, "/World/WaterSurface")
    water_surface.CreatePointsAttr([
        (-50, -50, 0), (50, -50, 0), (50, 50, 0), (-50, 50, 0)
    ])
    water_surface.CreateFaceVertexIndicesAttr([0, 1, 2, 3])
    water_surface.CreateFaceVertexCountsAttr([4])
    water_surface.CreateDisplayColorAttr([(0.2, 0.6, 0.8)])
    
    # 海底
    sea_floor = UsdGeom.Mesh.Define(stage, "/World/SeaFloor")
    sea_floor.CreatePointsAttr([
        (-100, -100, -10), (100, -100, -10), (100, 100, -10), (-100, 100, -10)
    ])
    sea_floor.CreateFaceVertexIndicesAttr([0, 1, 2, 3])
    sea_floor.CreateFaceVertexCountsAttr([4])
    sea_floor.CreateDisplayColorAttr([(0.4, 0.3, 0.2)])
    
    # 环境光
    light = UsdGeom.DistantLight.Define(stage, "/World/SunLight")
    light.CreateIntensityAttr(1000)
    light.CreateColorAttr((0.8, 0.9, 1.0))
    
    print("✅ 基础水下环境创建完成")
```

## 📁 文件组织建议

### 推荐的项目结构
```
your_project/
├── standalone_multi_rov_system.py
├── ROV_THRUSTERS.usd              # 您的环境文件
├── assets/
│   └── environments/
│       └── ROV_THRUSTERS.usd      # 备用位置
└── README.md
```

### 使用说明
1. **将 `ROV_THRUSTERS.usd` 放在脚本同目录下**（最简单）
2. **或者修改脚本中的路径**指向您的 USD 文件位置
3. **或者使用绝对路径**确保文件能被找到

## 🔧 自定义环境加载

如果您需要加载特定的环境配置：

```python
def load_custom_rov_environment():
    """加载自定义 ROV 环境"""
    # 您的 USD 文件路径
    usd_file_path = "path/to/your/ROV_THRUSTERS.usd"
    
    # 加载主环境
    env_prim = stage.DefinePrim("/World/ROVEnvironment", "Xform")
    env_prim.GetReferences().AddReference(usd_file_path)
    
    # 可以添加额外的环境元素
    # 例如：额外的推进器、障碍物、传感器等
    
    return True
```

## ⚠️ 重要注意事项

1. **文件路径**: 确保 USD 文件路径正确
2. **文件权限**: 确保文件可读
3. **USD 格式**: 确保文件是有效的 USD 格式
4. **依赖项**: 如果 USD 文件引用了其他资源，确保这些资源也可访问
5. **加载顺序**: 环境应该在创建 ROV 之前加载

## 🚀 完整示例

```python
# 在 standalone_multi_rov_system.py 中的使用
def main():
    print("🌊 加载 ROV 操作环境...")
    
    # 尝试加载您的 USD 环境文件
    if not load_usd_environment():
        print("⚠️ 使用基础环境")
    
    print("🤖 创建 ROV 系统...")
    success = run_multi_rov_simulation()
    
    return success
```

这样，独立模式就能够像 ActionGraph 模式一样使用您预先准备的 `ROV_THRUSTERS.usd` 环境文件了！
