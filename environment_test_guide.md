# 环境加载测试指南

## 🎯 目标

仅测试 USD 环境文件加载功能，暂时关闭所有 ROV 物理计算，专注于验证环境是否正确加载。

## 🔧 当前配置

### ✅ 已启用的功能
- SimulationApp 初始化
- SimulationContext 创建
- PhysX 接口获取
- USD 环境文件加载 (`ROV_THRUSTERS.usd`)
- 基础仿真环境启动
- 简单的可视化测试

### ❌ 已禁用的功能
- ROV 物理创建
- 浮力/阻力/控制力计算
- 复杂的仿真循环
- 多 ROV 系统逻辑

## 🚀 如何运行测试

### 步骤 1: 准备文件
```
确保您的项目目录结构如下：
your_project/
├── standalone_multi_rov_system.py
├── ROV_THRUSTERS.usd              # 您的环境文件
└── other_files...
```

### 步骤 2: 运行测试
```bash
cd /path/to/your/project
python standalone_multi_rov_system.py
```

### 步骤 3: 观察输出
您应该看到类似这样的输出：
```
======================================================================
🤖 独立模式多 ROV 水下系统
🔧 基于官方 Isaac Sim standalone 模式最佳实践
======================================================================

🌊 加载 USD 环境文件...
📁 找到 USD 文件: ROV_THRUSTERS.usd
✅ 成功加载环境: ROV_THRUSTERS.usd

🧪 当前模式：仅测试环境加载
🔧 物理计算和 ROV 创建已暂时禁用

🧪 测试模式：仅环境加载
==================================================

📋 初始化步骤:
1. ✅ SimulationApp 已启动
2. ✅ SimulationContext 已创建
3. ✅ PhysX 接口已获取
4. ✅ USD 环境已加载
5. 🔄 跳过物理仿真和 ROV 创建

🔄 启动基础仿真环境...

✅ 环境加载测试完成！
📋 检查项目:
  • Isaac Sim 窗口是否打开？
  • 是否看到了水下环境？
  • 是否加载了 ROV_THRUSTERS.usd 文件？
  • 控制台是否显示了正确的加载信息？

⏱️ 保持环境运行 10 秒供观察...
  ⏳ 1/10 秒
  ⏳ 2/10 秒
  ...
  ⏳ 10/10 秒

🛑 停止测试环境
✅ 环境加载测试完成
🏁 程序执行完成: True
🔄 关闭 Isaac Sim...
```

## 🔍 检查清单

### ✅ 成功的标志
- [ ] Isaac Sim 窗口打开
- [ ] 控制台显示 "✅ 成功加载环境: ROV_THRUSTERS.usd"
- [ ] 可以看到您的水下环境场景
- [ ] 没有错误信息
- [ ] 程序正常结束

### ❌ 可能的问题

#### 问题 1: 找不到 USD 文件
```
⚠️ 未找到 USD 文件: ROV_THRUSTERS.usd
🔧 将创建基础水下环境...
```
**解决方案**: 
- 确保 `ROV_THRUSTERS.usd` 在脚本同目录下
- 或修改脚本中的文件路径

#### 问题 2: USD 文件损坏
```
❌ 加载 USD 环境失败: [错误信息]
```
**解决方案**:
- 检查 USD 文件是否完整
- 尝试在 Isaac Sim 中手动打开该文件

#### 问题 3: Isaac Sim 启动失败
```
❌ 测试错误: [启动相关错误]
```
**解决方案**:
- 检查 Isaac Sim 安装
- 确保环境变量正确设置

## 🔄 下一步操作

### 如果环境加载成功
1. **观察环境**: 确认您的 USD 环境正确显示
2. **启用 ROV 创建**: 可以逐步启用 ROV 相关功能
3. **启用物理计算**: 最后启用完整的物理仿真

### 如果需要启用完整功能
修改 `standalone_multi_rov_system.py` 中的主函数：

```python
# 将这行：
success = test_environment_loading_only()

# 改为：
success = run_multi_rov_simulation_DISABLED()  # 先改名
# 然后重新实现完整功能
```

## 🛠️ 自定义环境路径

如果您的 USD 文件在其他位置，修改这部分代码：

```python
def load_usd_environment():
    # 修改这里的路径
    usd_file_path = "your/custom/path/ROV_THRUSTERS.usd"
    
    # 或者使用绝对路径
    # usd_file_path = "C:/full/path/to/ROV_THRUSTERS.usd"
```

## 📝 测试日志

记录您的测试结果：

```
测试日期: ___________
Isaac Sim 版本: ___________
USD 文件路径: ___________

测试结果:
□ SimulationApp 启动成功
□ USD 环境加载成功  
□ Isaac Sim 窗口显示正常
□ 环境场景显示正确
□ 无错误信息

问题记录:
_________________________________
_________________________________
_________________________________
```

这个简化版本让您可以专注于验证环境加载功能，确保基础架构正常工作后再逐步添加其他功能。
