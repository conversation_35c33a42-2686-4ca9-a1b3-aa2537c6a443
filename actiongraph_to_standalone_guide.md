# ActionGraph 转独立模式完整转换指南

## 📋 概述

本指南详细说明如何将您的 `complete_multi_rov_system.py` 从 ActionGraph 模式转换为独立 Python 脚本模式。

## 🔄 核心转换对比

### 1. 导入模块变化

#### ActionGraph 模式 (原始)
```python
import omni.graph.core as og
import omni.kit.commands
import carb
import omni.usd
from pxr import UsdGeom, Gf, UsdPhysics
```

#### 独立模式 (转换后)
```python
import omni.timeline          # 时间轴控制
import omni.physx            # 物理引擎接口
import omni.usd              # USD 场景操作
import carb                  # 日志系统
import asyncio               # 异步编程
import math, time, threading # 基础模块
from pxr import UsdGeom, Gf, UsdPhysics
from omni.isaac.core.utils.physics import simulate_async
from omni.isaac.core.utils.prims import get_prim_at_path
```

### 2. 系统架构变化

#### ActionGraph 模式架构
```
ActionGraph 系统
├── OnTick 节点 (触发器)
├── ScriptNode 节点们
│   ├── ROV 物理脚本 (字符串形式)
│   ├── 环境系统脚本
│   └── 监控脚本
├── 节点连接 (数据流)
└── 自动执行
```

#### 独立模式架构
```
独立 Python 系统
├── 主控制类 (StandaloneMultiROVSystem)
├── ROV 控制器类们 (ROVPhysicsController)
├── 环境系统类 (EnvironmentalSystem)
├── 异步仿真循环 (simulation_loop)
└── 手动控制启停
```

## 🔧 具体转换步骤

### 步骤 1: 替换 ActionGraph 脚本为类方法

#### 原始 ActionGraph 脚本
```python
def get_rov_physics_script(self, rov_index):
    return f'''
import math
import omni.usd
import time

def setup(db):
    db.rov_name = "{rov["name"]}"
    db.rov_path = "{rov["path"]}"
    # ... 初始化参数

def compute(db):
    # ... 物理计算
    db.outputs.total_force = total_force
'''
```

#### 转换为独立类
```python
class ROVPhysicsController:
    def __init__(self, rov_config):
        self.rov_name = rov_config["name"]
        self.rov_path = rov_config["path"]
        # ... 直接初始化参数
    
    def update_physics(self, dt):
        # ... 物理计算逻辑
        self.apply_force_to_prim(prim, total_force)
```

### 步骤 2: 替换 ActionGraph 执行机制

#### 原始 ActionGraph 执行
```python
def create_multi_rov_physics_graph(self):
    keys = og.Controller.Keys
    (graph_handle, nodes_created, _, _) = og.Controller.edit(
        {"graph_path": self.graph_path, "evaluator_name": "execution"},
        {
            keys.CREATE_NODES: [
                ("tick", "omni.graph.action.OnTick"),  # 自动触发
                # ... 其他节点
            ],
            keys.CONNECT: [
                # ... 节点连接
            ],
        },
    )
```

#### 转换为异步循环
```python
async def simulation_loop(self):
    """主仿真循环 - 替代 ActionGraph 的 OnTick"""
    while self.is_running:
        dt = 1.0 / 60.0  # 60 FPS
        
        # 更新环境系统
        self.environmental_system.update_environment(dt)
        
        # 更新每个 ROV
        for controller in self.rov_controllers:
            controller.update_physics(dt)
        
        # 等待下一帧
        await simulate_async(dt)
```

### 步骤 3: 替换力应用机制

#### 原始 ActionGraph 方式
```python
# 在 ScriptNode 中输出到 ApplyForce 节点
db.outputs.total_force = total_force
```

#### 转换为直接 API 调用
```python
def apply_force_to_prim(self, prim, force_z):
    """直接向 prim 应用力"""
    try:
        physx = omni.physx.get_physx_interface()
        if physx:
            physx.apply_force_at_pos(
                prim.GetPath().pathString,
                [0, 0, force_z],  # 力向量
                [0, 0, 0],        # 应用点
                "Force"           # 力类型
            )
    except Exception as e:
        print(f"力应用失败: {e}")
```

## 🚀 使用新的独立模式系统

### 基本使用方法
```python
# 创建系统
system = StandaloneMultiROVSystem()

# 启动仿真
system.start_simulation()

# 获取状态
status = system.get_system_status()

# 停止仿真
system.stop_simulation()
```

### 高级控制
```python
# 手动控制单个 ROV
rov_controller = system.rov_controllers[0]
rov_controller.target_depth = -3.0  # 修改目标深度

# 修改环境参数
system.environmental_system.wave_amplitude = 0.6
system.environmental_system.current_strength = 0.5

# 添加新的 ROV
new_rov_config = {
    "name": "ROV_New",
    "path": "/World/ROV_New",
    "mass": 750,
    "size": 1.9,
    "target_depth": -2.5,
    "color": (0.8, 0.8, 0.2)
}
new_controller = ROVPhysicsController(new_rov_config)
system.rov_controllers.append(new_controller)
```

## ⚡ 性能和优势对比

### ActionGraph 模式
- ✅ 可视化编程界面
- ✅ 自动执行和连接
- ❌ 调试困难
- ❌ 代码复用性差
- ❌ 性能开销较大
- ❌ 扩展性有限

### 独立模式
- ✅ 完全的 Python 控制
- ✅ 易于调试和测试
- ✅ 高度可扩展
- ✅ 更好的性能
- ✅ 代码复用性强
- ❌ 需要手动管理执行流程

## 🔍 关键差异总结

| 方面 | ActionGraph 模式 | 独立模式 |
|------|------------------|----------|
| **执行触发** | OnTick 节点自动触发 | 异步循环手动控制 |
| **代码组织** | 字符串形式的脚本 | 标准 Python 类和方法 |
| **数据传递** | 节点输入/输出连接 | 直接方法调用和参数传递 |
| **力应用** | ApplyForce 节点 | 直接 PhysX API 调用 |
| **调试** | 通过 ActionGraph 界面 | 标准 Python 调试工具 |
| **扩展性** | 需要修改图结构 | 直接添加类和方法 |
| **性能** | 图执行开销 | 直接函数调用 |

## 📝 迁移检查清单

- [ ] 替换所有 ActionGraph 相关导入
- [ ] 将 ScriptNode 脚本转换为类方法
- [ ] 实现异步仿真循环
- [ ] 替换力应用机制
- [ ] 添加手动启停控制
- [ ] 测试所有 ROV 的物理行为
- [ ] 验证环境效果
- [ ] 确认监控和日志功能

## 🎯 下一步建议

1. **测试独立模式**: 运行 `standalone_multi_rov_system.py`
2. **性能对比**: 比较两种模式的执行效率
3. **功能扩展**: 利用独立模式的灵活性添加新功能
4. **集成测试**: 确保所有 ROV 行为符合预期
5. **文档更新**: 更新使用说明和 API 文档
