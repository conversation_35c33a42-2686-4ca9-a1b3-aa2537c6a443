# 更新后的使用指南

## 🎯 更新内容

已将**方法1（直接打开USD文件）**替换为默认的加载方式，现在所有脚本都使用与双击打开相同的方法。

## 🚀 更新的脚本

### 1. `simple_usd_loader.py` - 极简版本
```python
# 现在使用方法1：直接打开USD文件
omni.usd.get_context().close_stage()
success = omni.usd.get_context().open_stage(abs_path)
```

### 2. `standalone_multi_rov_system.py` - 完整版本
```python
# 现在使用方法1：直接打开USD文件
omni.usd.get_context().close_stage()
success = omni.usd.get_context().open_stage(abs_path)
# 重新获取stage
global stage
stage = omni.usd.get_context().get_stage()
```

## 🔄 关键改进

### ✅ 之前的问题
- 使用引用方式加载：`env_prim.GetReferences().AddReference()`
- 场景结构：`/World/Environment/[USD内容]`
- 与双击打开的结果不同

### ✅ 现在的解决方案
- 使用直接打开方式：`omni.usd.get_context().open_stage()`
- 场景结构：`/World`, `/Environment` 在根级别
- 与双击打开的结果完全相同

## 📋 使用方法

### 极简版本（推荐日常使用）
```bash
python simple_usd_loader.py
```

**特点**：
- ✅ 只加载USD环境并保持运行
- ✅ 完全模拟双击打开行为
- ✅ 显示场景结构信息
- ✅ 静默运行，按Ctrl+C停止

### 完整版本（用于开发测试）
```bash
python standalone_multi_rov_system.py
```

**特点**：
- ✅ 使用相同的USD加载方法
- ✅ 包含ROV物理仿真功能（当前禁用）
- ✅ 可以逐步启用其他功能

## 🔍 验证结果

运行脚本后，您应该看到：

### 控制台输出
```
🚀 启动 Isaac Sim USD 加载器
==================================================
📁 找到文件: ROV_THRUSTERS.usd
📍 绝对路径: C:\your\path\ROV_THRUSTERS.usd
🔄 直接打开USD文件作为主场景...
✅ USD文件已作为主场景打开
📋 场景结构:
  📁 /World
  📁 /Environment
==================================================
✅ USD文件加载完成！
💡 现在的场景应该和您双击打开的完全一样
```

### Isaac Sim界面
- 场景结构与双击打开完全相同
- World 和 Environment 都在根级别
- 视觉效果完全一致

## 🛠️ 故障排除

### 如果仍有差异
1. **检查控制台输出**：确认显示"✅ USD文件已作为主场景打开"
2. **检查场景结构**：在Scene Hierarchy面板中确认结构
3. **检查文件路径**：确认使用了正确的绝对路径

### 如果加载失败
- 确保 `ROV_THRUSTERS.usd` 文件在脚本同目录下
- 检查文件是否损坏
- 查看控制台的具体错误信息

## 📁 文件结构
```
your_project/
├── simple_usd_loader.py           # 极简版本（推荐）
├── standalone_multi_rov_system.py # 完整版本
├── usd_loader_exact.py           # 多方法版本
├── ROV_THRUSTERS.usd             # 您的USD文件
└── README.md
```

## 🎉 总结

现在所有脚本都使用**方法1**直接打开USD文件，完全模拟您双击打开的行为。场景结构和视觉效果应该与手动打开完全一致！
