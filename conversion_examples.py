"""
ActionGraph 转独立模式 - 关键代码片段对比示例
"""

# ============================================================================
# 1. 导入模块对比
# ============================================================================

# ActionGraph 模式导入
"""
import omni.graph.core as og
import omni.kit.commands
import carb
import omni.usd
from pxr import UsdGeom, Gf, UsdPhysics
"""

# 独立模式导入
"""
import omni.timeline
import omni.physx
import omni.usd
import carb
import asyncio
import math
import time
from pxr import UsdGeom, Gf, UsdPhysics
from omni.isaac.core.utils.physics import simulate_async
"""

# ============================================================================
# 2. ROV 物理计算对比
# ============================================================================

# ActionGraph 模式 - ScriptNode 字符串脚本
def actiongraph_rov_physics_script():
    return '''
import math
import omni.usd
import time

def setup(db):
    db.rov_name = "ROV_Main"
    db.rov_path = "/World/ROV_Main"
    db.mass = 1000
    db.target_depth = -4.0
    db.water_density = 1025.0
    db.gravity = 9.81

def compute(db):
    stage = omni.usd.get_context().get_stage()
    rov_prim = stage.GetPrimAtPath(db.rov_path)
    
    if rov_prim.IsValid():
        xform = rov_prim.GetAttribute("xformOp:translate")
        position = xform.Get()
        
        # 计算浮力
        depth = 0.0 - position[2]
        buoyancy_force = db.water_density * depth * db.gravity
        
        # 输出到 ActionGraph 节点
        db.outputs.buoyancy_force = buoyancy_force
        db.outputs.depth = depth
'''

# 独立模式 - Python 类方法
class StandaloneROVPhysics:
    def __init__(self, rov_config):
        self.rov_name = rov_config["name"]
        self.rov_path = rov_config["path"]
        self.mass = rov_config["mass"]
        self.target_depth = rov_config["target_depth"]
        self.water_density = 1025.0
        self.gravity = 9.81
    
    def update_physics(self, dt):
        """每帧更新物理计算"""
        stage = omni.usd.get_context().get_stage()
        rov_prim = stage.GetPrimAtPath(self.rov_path)
        
        if rov_prim.IsValid():
            position = self.get_prim_position(rov_prim)
            
            # 计算浮力
            depth = 0.0 - position[2]
            buoyancy_force = self.water_density * depth * self.gravity
            
            # 直接应用力
            self.apply_force_to_prim(rov_prim, buoyancy_force)
            
            return {
                'buoyancy_force': buoyancy_force,
                'depth': depth
            }

# ============================================================================
# 3. 执行机制对比
# ============================================================================

# ActionGraph 模式 - 图创建和连接
def actiongraph_execution():
    """ActionGraph 自动执行机制"""
    keys = og.Controller.Keys
    (graph_handle, nodes_created, _, _) = og.Controller.edit(
        {"graph_path": "/MultiROVSystem", "evaluator_name": "execution"},
        {
            keys.CREATE_NODES: [
                ("tick", "omni.graph.action.OnTick"),           # 自动触发
                ("rov_physics", "omni.graph.scriptnode.ScriptNode"),
                ("apply_force", "omni.graph.nodes.ApplyForce"),
                ("debug_print", "omni.graph.ui_nodes.PrintText"),
            ],
            keys.SET_VALUES: [
                ("rov_physics.inputs:script", actiongraph_rov_physics_script()),
            ],
            keys.CONNECT: [
                ("tick.outputs:tick", "rov_physics.inputs:execIn"),
                ("rov_physics.outputs:buoyancy_force", "apply_force.inputs:force"),
                ("rov_physics.outputs:execOut", "debug_print.inputs:execIn"),
            ],
        },
    )

# 独立模式 - 异步循环控制
class StandaloneExecution:
    def __init__(self):
        self.timeline = omni.timeline.get_timeline_interface()
        self.is_running = False
        self.rov_physics = StandaloneROVPhysics({
            "name": "ROV_Main",
            "path": "/World/ROV_Main",
            "mass": 1000,
            "target_depth": -4.0
        })
    
    async def simulation_loop(self):
        """手动控制的仿真循环"""
        while self.is_running:
            dt = 1.0 / 60.0  # 60 FPS
            
            # 更新物理
            result = self.rov_physics.update_physics(dt)
            
            # 调试输出
            if result:
                print(f"浮力: {result['buoyancy_force']:.2f}N, 深度: {result['depth']:.2f}m")
            
            # 等待下一帧
            await simulate_async(dt)
    
    def start_simulation(self):
        """启动仿真"""
        self.is_running = True
        self.timeline.play()
        asyncio.ensure_future(self.simulation_loop())
    
    def stop_simulation(self):
        """停止仿真"""
        self.is_running = False
        self.timeline.stop()

# ============================================================================
# 4. 力应用机制对比
# ============================================================================

# ActionGraph 模式 - 通过节点输出
def actiongraph_force_application():
    """在 ScriptNode 的 compute 函数中"""
    # 计算力
    total_force = buoyancy_force + control_force + drag_force
    
    # 输出到 ApplyForce 节点
    # db.outputs.total_force = total_force  # 这会自动连接到 ApplyForce 节点

# 独立模式 - 直接 API 调用
def standalone_force_application(self, prim, force_vector):
    """直接应用力到 prim"""
    try:
        # 方法 1: 使用 PhysX 接口
        physx = omni.physx.get_physx_interface()
        if physx:
            prim_path = prim.GetPath().pathString
            physx.apply_force_at_pos(
                prim_path,
                [0, 0, force_vector],  # 力向量
                [0, 0, 0],             # 应用点（质心）
                "Force"                # 力类型
            )
        
        # 方法 2: 使用 Isaac Core API (备选)
        from omni.isaac.core.prims.rigid_prim import RigidPrim
        rigid_prim = RigidPrim(prim.GetPath().pathString)
        rigid_prim.apply_force([0, 0, force_vector])
        
    except Exception as e:
        print(f"力应用失败: {e}")

# ============================================================================
# 5. 系统初始化对比
# ============================================================================

# ActionGraph 模式初始化
def actiongraph_initialization():
    """ActionGraph 系统初始化"""
    system = SimpleMultiROVSystem()
    
    # 1. 确保物理属性
    system.ensure_rov_physics()
    
    # 2. 创建 ActionGraph
    system.create_multi_rov_physics_graph()
    
    # 3. 自动开始执行（点击 Play）
    print("ActionGraph 已创建，点击 Play 开始仿真")

# 独立模式初始化
def standalone_initialization():
    """独立模式系统初始化"""
    system = StandaloneExecution()
    
    # 1. 确保物理属性
    system.ensure_rov_physics()  # 如果需要
    
    # 2. 手动启动仿真
    system.start_simulation()
    
    # 3. 可以随时控制
    print("独立模式仿真已启动")
    
    # 稍后停止
    # system.stop_simulation()

# ============================================================================
# 6. 调试和监控对比
# ============================================================================

# ActionGraph 模式调试
def actiongraph_debugging():
    """ActionGraph 调试方式"""
    # 1. 在 ScriptNode 中添加 print 语句
    # 2. 使用 ActionGraph 界面查看节点值
    # 3. 通过 PrintText 节点输出信息
    pass

# 独立模式调试
def standalone_debugging():
    """独立模式调试方式"""
    # 1. 标准 Python 调试工具
    import pdb; pdb.set_trace()
    
    # 2. 直接 print 调试
    print(f"ROV 位置: {position}")
    
    # 3. 日志系统
    import logging
    logging.info("仿真状态更新")
    
    # 4. 实时状态查询
    status = system.get_system_status()
    print(f"系统状态: {status}")

# ============================================================================
# 7. 使用示例
# ============================================================================

def usage_example():
    """使用示例"""
    print("=== ActionGraph 模式使用 ===")
    print("1. 运行: exec(open('complete_multi_rov_system.py').read())")
    print("2. 调用: main()")
    print("3. 点击 Isaac Sim 的 Play 按钮")
    print("4. 观察 ActionGraph 界面和控制台输出")
    
    print("\n=== 独立模式使用 ===")
    print("1. 运行: exec(open('standalone_multi_rov_system.py').read())")
    print("2. 调用: system = main()")
    print("3. 启动: system.start_simulation()")
    print("4. 停止: system.stop_simulation()")
    print("5. 状态: system.get_system_status()")

if __name__ == "__main__":
    usage_example()
