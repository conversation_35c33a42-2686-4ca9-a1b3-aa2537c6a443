"""
极简 USD 环境加载器
只加载 ROV_THRUSTERS.usd 文件并保持运行
"""

from isaacsim import SimulationApp

# 启动 <PERSON>（非无头模式，显示界面）
simulation_app = SimulationApp({"headless": False})

import carb
import omni
import os
from isaacsim.core.api import SimulationContext
from pxr import UsdGeom

def main():
    """主函数：加载USD环境并保持运行"""
    print("🚀 启动 Isaac Sim USD 加载器")
    print("=" * 50)
    
    try:
        # 初始化核心组件
        stage = simulation_app.context.get_stage()
        sim_context = SimulationContext(stage_units_in_meters=1.0)
        
        # 加载 ROV_THRUSTERS.usd 文件
        usd_file_path = "ROV_THRUSTERS.usd"
        
        if os.path.exists(usd_file_path):
            print(f"📁 找到文件: {usd_file_path}")
            
            # 加载 USD 文件到场景
            env_prim = stage.DefinePrim("/World/Environment", "Xform")
            env_prim.GetReferences().AddReference(usd_file_path)
            
            print(f"✅ 成功加载: {usd_file_path}")
        else:
            print(f"❌ 未找到文件: {usd_file_path}")
            print("请确保 ROV_THRUSTERS.usd 文件在脚本同目录下")
            return False
        
        # 启动仿真
        physx_interface = omni.physx.acquire_physx_interface()
        physx_interface.start_simulation()
        sim_context._timeline.play()
        
        print("✅ 环境加载完成，开始运行...")
        print("💡 按 Ctrl+C 可以停止程序")
        print("=" * 50)
        
        # 静默运行循环
        frame_count = 0
        while True:
            # 保持应用更新
            simulation_app.update()
            
            # 每 10 秒输出一次简单状态（可选）
            if frame_count % 600 == 0 and frame_count > 0:
                print(f"🔄 运行中... ({frame_count // 60} 秒)")
            
            frame_count += 1
            
            # 控制帧率 (60 FPS)
            import time
            time.sleep(1.0 / 60.0)
            
    except KeyboardInterrupt:
        print("\n🛑 用户停止程序")
        return True
    except Exception as e:
        print(f"\n❌ 运行错误: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
    except Exception as e:
        print(f"❌ 程序错误: {e}")
    finally:
        # 关闭 Isaac Sim
        print("🔄 关闭 Isaac Sim...")
        simulation_app.close()
        print("✅ 程序结束")
